import 'package:flutter/material.dart';
import 'package:media_kit/media_kit.dart';
import 'package:get_it/get_it.dart';

// 导入环境配置
import 'config/environment.dart';

// 导入新的服务
import 'services/service_locator.dart';
import 'services/api_service.dart';
import 'services/token_manager.dart';
import 'services/theme_service.dart';

// 导入页面
import 'pages/login_page.dart';
import 'pages/home_page.dart';
import 'pages/settings_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 必要的 media_kit 初始化
  MediaKit.ensureInitialized();
  
  // 初始化依赖注入容器
  await ServiceLocator.setupServices();
  
  // 初始化新的API服务
  ApiService().initialize();
  
  // 初始化主题服务
  await ThemeService().initialize();
  
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  final ThemeService _themeService = ThemeService();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _themeService.addListener(_onThemeChanged);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _themeService.removeListener(_onThemeChanged);
    
    // 应用退出时释放资源
    ApiService().dispose();
    
    super.dispose();
  }

  void _onThemeChanged() {
    setState(() {});
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    // 当应用进入后台时，可以做一些清理工作
    if (state == AppLifecycleState.paused) {
      // 这里可以清理一些缓存等
      // ApiService().clearAllCache(); // 如果需要的话
    }
  }

  @override
  void didChangePlatformBrightness() {
    super.didChangePlatformBrightness();
    final brightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
    _themeService.updateSystemBrightness(brightness);
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: EnvironmentConfig.appName,
      theme: ThemeService.lightTheme,
      darkTheme: ThemeService.darkTheme,
      themeMode: _themeService.flutterThemeMode,
      home: const SplashScreen(),
      routes: {
        '/login': (context) => const LoginPage(),
        '/home': (context) => const HomePage(),
        '/settings': (context) => const SettingsPage(),
      },
      // 添加全局错误处理
      builder: (context, child) {
        // 可以在这里添加全局错误边界
        ErrorWidget.builder = (FlutterErrorDetails errorDetails) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  const Text(
                    '应用遇到了问题',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '错误信息: ${errorDetails.exception}',
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          );
        };
        return child!;
      },
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  late final ApiService _apiService;
  late final TokenManager _tokenManager;
  
  @override
  void initState() {
    super.initState();
    _apiService = ApiService();
    _tokenManager = GetIt.instance<TokenManager>();
    _checkLoginStatus();
  }

  /// 显示错误对话框
  void _showErrorDialog(String title, String message, {VoidCallback? onRetry}) {
    if (!mounted) return;
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.warning, color: Colors.orange),
              const SizedBox(width: 8),
              Text(title),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(message),
                const SizedBox(height: 16),
                // 显示调试信息（仅在调试模式下）
                if (const bool.fromEnvironment('dart.vm.product') == false) ...[
                  const Divider(),
                  const Text(
                    '调试信息:',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                  ),
                  FutureBuilder<Map<String, dynamic>>(
                    future: _tokenManager.getTokenStatus(),
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        final status = snapshot.data!;
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('有访问令牌: ${status['hasAccessToken']}', 
                                 style: const TextStyle(fontSize: 11, fontFamily: 'monospace')),
                            Text('有刷新令牌: ${status['hasRefreshToken']}', 
                                 style: const TextStyle(fontSize: 11, fontFamily: 'monospace')),
                            Text('即将过期: ${status['isExpiringSoon']}', 
                                 style: const TextStyle(fontSize: 11, fontFamily: 'monospace')),
                            Text('正在刷新: ${status['isRefreshing']}', 
                                 style: const TextStyle(fontSize: 11, fontFamily: 'monospace')),
                          ],
                        );
                      }
                      return const Text('获取状态中...', style: TextStyle(fontSize: 11));
                    },
                  ),
                ],
              ],
            ),
          ),
          actions: [
            if (onRetry != null)
              TextButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  onRetry();
                },
                icon: const Icon(Icons.refresh),
                label: const Text('重试'),
              ),
            TextButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pushReplacementNamed('/login');
              },
              icon: const Icon(Icons.login),
              label: const Text('重新登录'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _checkLoginStatus() async {
    try {
      // 检查是否有Token
      if (!await _tokenManager.hasValidTokens()) {
        // 没有Token，直接跳转到登录页
        if (mounted) {
          Navigator.of(context).pushReplacementNamed('/login');
        }
        return;
      }

      // 验证Token是否仍然有效，设置超时时间避免卡住
      final validationResult = await _apiService.isTokenValid().timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          // 超时时返回网络错误结果
          return TokenValidationResult.networkError('验证超时，请检查网络连接');
        },
      );
      
      if (!mounted) return;

      switch (validationResult.status) {
        case TokenValidationStatus.valid:
          // Token有效，进入主页
          if (validationResult.wasRefreshed) {
            // 如果Token被刷新了，显示提示
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('登录状态已自动刷新'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
          }
          Navigator.of(context).pushReplacementNamed('/home');
          break;
          
        case TokenValidationStatus.noToken:
          // 没有Token，跳转到登录页
          Navigator.of(context).pushReplacementNamed('/login');
          break;
          
        case TokenValidationStatus.needsRelogin:
          // 需要重新登录
          await _apiService.clearTokens();
          _showErrorDialog(
            '登录已过期',
            validationResult.errorMessage ?? '您的登录状态已过期，请重新登录',
          );
          break;
          
        case TokenValidationStatus.refreshFailed:
          // 刷新失败
          await _apiService.clearTokens();
          _showErrorDialog(
            '登录刷新失败',
            validationResult.errorMessage ?? '无法刷新登录状态，请重新登录',
          );
          break;
          
        case TokenValidationStatus.networkError:
          // 网络错误，提供重试选项
          _showErrorDialog(
            '网络连接错误',
            validationResult.errorMessage ?? '无法连接到服务器，请检查网络连接',
            onRetry: _checkLoginStatus,
          );
          break;
          
        case TokenValidationStatus.unknownError:
          // 未知错误，提供重试选项
          _showErrorDialog(
            '验证失败',
            validationResult.errorMessage ?? '登录验证遇到未知错误',
            onRetry: _checkLoginStatus,
          );
          break;
      }
    } catch (e) {
      // 发生异常时的处理
      if (const bool.fromEnvironment('dart.vm.product') == false) {
        // ignore: avoid_print
        print('启动检查出错: $e');
      }
      
      if (mounted) {
        // 检查是否有存储的token来决定默认行为
        try {
          final hasToken = await _tokenManager.hasValidTokens();
          
          if (hasToken) {
            // 有token但验证出错，提供重试选项
            _showErrorDialog(
              '启动检查失败',
              '无法验证登录状态：$e\n\n这可能是临时的网络问题。',
              onRetry: _checkLoginStatus,
            );
          } else {
            // 没有token，直接跳转到登录页
            Navigator.of(context).pushReplacementNamed('/login');
          }
        } catch (e2) {
          // 如果连安全存储都失败了，显示错误并提供重试
          _showErrorDialog(
            '应用启动失败',
            '应用初始化遇到问题：$e2\n\n这可能是首次启动或安全存储问题。',
            onRetry: _checkLoginStatus,
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).primaryColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 应用图标
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.cloud,
                size: 80,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 24),
            
            // 应用标题
            const Text(
              '115 API 客户端',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            
            // 版本信息
            Text(
              '基于Dio的健壮Token管理',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white.withOpacity(0.8),
              ),
            ),
            const SizedBox(height: 40),
            
            // 加载指示器
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              strokeWidth: 3,
            ),
            const SizedBox(height: 16),
            
            // 加载文字
            Text(
              '正在验证登录状态...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white.withOpacity(0.9),
              ),
            ),
            const SizedBox(height: 40),
            
            // 功能特性展示（调试模式下显示）
            if (const bool.fromEnvironment('dart.vm.product') == false) ...[
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 40),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.security, color: Colors.white, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          '安全存储',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.9),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.autorenew, color: Colors.white, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          '自动刷新Token',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.9),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.speed, color: Colors.white, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          'Dio拦截器',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.9),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
